import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { AdvanclyRepaymentTasks } from "./AdvanclyRepaymentTasks";
import { PersonalLoanRepaymentDefaultCharges } from "./PersonalLoanRepaymentDefaultCharges";
import { RepaymentWalletHistories } from "./RepaymentWalletHistories";
import { RepaymentWalletTransactions } from "./RepaymentWalletTransactions";
import { Loans } from "./Loans";
import { Users } from "./Users";

@Index("repayments_defaulted_index", ["defaulted"], {})
@Index("repayments_due_date_index", ["dueDate"], {})
@Index("repayments_loan_id_foreign", ["loanId"], {})
@Index("repayments_paid_at_index", ["paidAt"], {})
@Index("repayments_payment_method_index", ["paymentMethod"], {})
@Index("repayments_settlement_status_index", ["settlementStatus"], {})
@Index("repayments_status_index", ["status"], {})
@Index("repayments_user_id_foreign", ["userId"], {})
@Entity("repayments")
export class Repayments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "loan_id", unsigned: true })
  loanId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "reference", nullable: true, length: 255 })
  reference: string | null;

  @Column("double", { name: "amount", precision: 15, scale: 2 })
  amount: number;

  @Column("double", {
    name: "amount_paid",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  amountPaid: number | null;

  @Column("date", { name: "due_date" })
  dueDate: string;

  @Column("varchar", {
    name: "status",
    nullable: true,
    length: 250,
    default: () => "'not_due'",
  })
  status: string | null;

  @Column("tinyint", { name: "defaulted", width: 1, default: () => "'0'" })
  defaulted: boolean;

  @Column("datetime", { name: "paid_at", nullable: true })
  paidAt: Date | null;

  @Column("bigint", { name: "paid_by", nullable: true, unsigned: true })
  paidBy: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("datetime", { name: "in_advancly_at", nullable: true })
  inAdvanclyAt: Date | null;

  @Column("varchar", { name: "payment_method", nullable: true, length: 255 })
  paymentMethod: string | null;

  @Column("varchar", { name: "settlement_status", nullable: true, length: 255 })
  settlementStatus: string | null;

  @Column("timestamp", { name: "settled_at", nullable: true })
  settledAt: Date | null;

  @OneToMany(
    () => AdvanclyRepaymentTasks,
    (advanclyRepaymentTasks) => advanclyRepaymentTasks.repayment
  )
  advanclyRepaymentTasks: AdvanclyRepaymentTasks[];

  @OneToMany(
    () => PersonalLoanRepaymentDefaultCharges,
    (personalLoanRepaymentDefaultCharges) =>
      personalLoanRepaymentDefaultCharges.repayment
  )
  personalLoanRepaymentDefaultCharges: PersonalLoanRepaymentDefaultCharges[];

  @OneToMany(
    () => RepaymentWalletHistories,
    (repaymentWalletHistories) => repaymentWalletHistories.repayment
  )
  repaymentWalletHistories: RepaymentWalletHistories[];

  @OneToMany(
    () => RepaymentWalletTransactions,
    (repaymentWalletTransactions) => repaymentWalletTransactions.repayment
  )
  repaymentWalletTransactions: RepaymentWalletTransactions[];

  @ManyToOne(() => Loans, (loans) => loans.repayments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "loan_id", referencedColumnName: "id" }])
  loan: Loans;

  @ManyToOne(() => Users, (users) => users.repayments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
