import { Injectable } from "@nestjs/common";
import { Users } from "../../config/entities/Users";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class UserRepository extends TypeOrmRepository<Users> {
  constructor(private readonly dataSource: DataSource) {
    super(Users, dataSource.createEntityManager());
  }
}
