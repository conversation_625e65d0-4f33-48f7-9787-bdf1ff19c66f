import { Injectable, Logger } from '@nestjs/common';
import { Analytics } from '@customerio/cdp-analytics-node';
import config from 'src/config';
import { CustomerIoPayload } from 'src/utils';
@Injectable()
export class CustomerIoService {
  private logger: Logger = new Logger(CustomerIoService.name);
  private readonly analytics: Analytics;
  constructor() {
    this.analytics = new Analytics({
      writeKey: config.customerIoApiKey,
      maxEventsInBatch: 1,
    });
  }
  async insertCustomer(payload: CustomerIoPayload) {
    try {
        return this.analytics.identify(payload);
    } catch (error) {
        this.logger.log(`could not insert customer: ${error.message}`);
    }
  }
}
