import { RabbitmqService } from '@crednet/utils';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MbsRepository } from 'src/bank-statement/repositories/mbs.repository';
import { DirectDebitTransactions } from 'src/config/entities/DirectDebitTransactions';
import { CpcashWalletsRepository } from 'src/credit/repository/cp-cash-wallets.repository';
import { PersonalCardAccountsRepository } from 'src/credit/repository/personal-card-account.repository';
import { UserPlansRepository } from 'src/credit/repository/user-plan.repository';
import { CustomerIoService } from 'src/customer-io/customer-io.service';
import { DirectDebitTransactionsRepository } from 'src/direct-debit/repository/direct-debit-transactions.repositrory';
import { DirectDebitRepository } from 'src/direct-debit/repository/direct-debit.repository';
import { UserRepository } from 'src/user/repository/user.repository';
import { CashEvents, Events, Exchanges } from 'src/utils';
import { IsNull, LessThan, MoreThan, MoreThanOrEqual, Not } from 'typeorm';

@Injectable()
export class CronService {
  constructor(
    private readonly userPlansRepository: UserPlansRepository,
    private readonly personalCardAccountsRepository: PersonalCardAccountsRepository,
    private readonly cpcashWalletsRepository: CpcashWalletsRepository,
    private readonly directDebitTransactions: DirectDebitTransactionsRepository,
    private readonly directDebitRepository: DirectDebitRepository,
    private readonly rmqService: RabbitmqService,
    private readonly eventEmitter: EventEmitter2,
    private readonly mbsRepository: MbsRepository,
    private readonly customerIoService: CustomerIoService,
    private readonly userRepository: UserRepository,
  ) {
    setTimeout(() => {
      // this.fetchStatementDocForPending(1);
    }, 14000);
  }

  async updateAccountMandateStatus(page: number) {
    const items = await this.personalCardAccountsRepository.findMany(
      {
        page,
        limit: 50,
      },
      {
        where: {},
      },
    );

    for (const item of items.items) {
      console.log(item.userId, item.userId);
      this.eventEmitter.emit(Events.UPDATE_ACCOUNT_MANDATE_STATUS, {
        userId: item.userId,
      });
    }

    if (page < items.meta.totalPages) {
      return this.updateAccountMandateStatus(++page);
    }
  }

  async verifyMandate(page: number) {
    const items = await this.directDebitRepository.findMany(
      {
        page,
        limit: 50,
      },
      {
        where: {
          status: 'initiated',
        },
      },
    );

    for (const item of items.items) {
      console.log(item.userId, item.userId);
      this.eventEmitter.emit(Events.CHECK_MANDATE_STATUS, {
        userId: item.userId,
      });
    }

    if (page < items.meta.totalPages) {
      return this.verifyMandate(++page);
    }
  }

  async verifyMandateTransactions(page: number) {
    const items = await this.directDebitTransactions.findMany(
      {
        page,
        limit: 50,
      },
      {
        where: {
          status: 'pending',
        },
      },
    );

    for (const item of items.items) {
      console.log(item.userId, item.userId);
      this.eventEmitter.emit(Events.VERIFY_MANDATE, {
        userId: item.userId,
        reference: item.reference,
      });
    }

    if (page < items.meta.totalPages) {
      return this.verifyMandateTransactions(++page);
    }
  }

  async pushUserIdToCash(page: number) {
    const items = await this.cpcashWalletsRepository.findMany(
      {
        page,
        limit: 50,
      },
      {
        where: {
          createdAt: MoreThan(new Date('2025-02-10')),
        },
      },
    );

    for (const item of items.items) {
      console.log(item.userId, item.userId);
      await this.rmqService.send(Exchanges.CASH, {
        key: CashEvents.UPDATE_USERID,
        data: {
          userId: item.userId,
          walletId: item.walletId,
        },
      });
    }

    if (page < items.meta.totalPages) {
      return this.pushUserIdToCash(++page);
    }
  }

  async syncAllRepaymentPercentage(page: number) {
    const items = await this.userPlansRepository.findMany(
      {
        page,
        limit: 50,
      },
      {
        where: {
          repaymentPercentage: LessThan('100.0000'),
          deletedAt: IsNull(),
          // status: 'active',
        },
      },
    );

    for (const item of items.items) {
      console.log(item.userId, item.repaymentPercentage);
      await this.personalCardAccountsRepository.update(
        {
          userId: item.userId,
        },
        {
          repaymentPercentage: item.repaymentPercentage,
        },
      );
    }

    if (page < items.meta.totalPages) {
      return this.syncAllRepaymentPercentage(++page);
    }
  }

  async fetchStatementDocForPending(page: number) {
    const items = await this.mbsRepository.findMany(
      {
        page,
        limit: 50,
      },
      {
        where: {
          hasSavedDoc: false,
          error: IsNull(),
          createdAt: MoreThan(new Date('2025-02-10')),
        },
        order: {
          createdAt: 'asc',
        },
      },
    );

    for (const item of items.items) {
      console.log(item.userId);
      this.eventEmitter.emit(Events.FETCH_STATEMENT_DOC, {
        userId: item.userId,
        id: item.id,
      });
    }

    if (page < items.meta.totalPages) {
      return this.fetchStatementDocForPending(++page);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async addUserToCustomerIo(page: number) {
    const items = await this.userRepository.findMany(
      {
        page,
        limit: 50,
      },
      {
        where: { createdAt: MoreThanOrEqual(new Date('2025-01-01')) },
      },
    );

    for (const item of items.items) {
      await this.customerIoService.insertCustomer({
        userId: item.id,
        traits: {
          email: item.email,
          name: item.name,
          lastName: item.lastName,
          phone: item.phoneNo,
        },
        timestamp: new Date().toUTCString(),
      });
    }

    if (page < items.meta.totalPages) {
      return this.updateAccountMandateStatus(++page);
    }
  }
}
