import { Module } from '@nestjs/common';
import { BankStatementController } from './bank-statement.controller';
import { BankStatementService } from './bank-statement.service';
import { StatementRepository } from './repositories/statement.repository';
import { MbsModule } from '@app/mbs';
import { UserRepository } from 'src/user/repository/user.repository';
import { MbsRepository } from './repositories/mbs.repository';
import { DocumentsRepository } from 'src/credit/repository/documents.repository';
import { VerificationModule } from '@app/verification';
import { SaveStatementConsumer } from './save-statement.consumer';
import { BullModule } from '@nestjs/bullmq';
import { Events } from 'src/utils';

@Module({
  imports: [
    MbsModule,
    VerificationModule,
    BullModule.registerQueue({ name: Events.SAVE_STATEMENT }),
  ],
  controllers: [BankStatementController],
  providers: [
    BankStatementService,
    StatementRepository,
    UserRepository,
    MbsRepository,
    DocumentsRepository,
    SaveStatementConsumer,
  ],
})
export class BankStatementModule {}
