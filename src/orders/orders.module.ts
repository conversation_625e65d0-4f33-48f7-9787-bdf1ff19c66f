import { Module } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { MerchantOrderRepository } from './repositories/orders.repository';
import { CardtItemsRepository } from './repositories/cart.items.repository';

@Module({
  controllers: [OrdersController],
  providers: [OrdersService, MerchantOrderRepository, CardtItemsRepository],
})
export class OrdersModule {}
