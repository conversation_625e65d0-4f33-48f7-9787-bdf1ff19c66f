import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { CartItems } from '../../config/entities/cartItems';

@Injectable()
export class CardtItemsRepository extends TypeOrmRepository<CartItems> {
  constructor(private readonly dataSource: DataSource) {
    super(CartItems, dataSource.createEntityManager());
  }
}
