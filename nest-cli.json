{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": false}, "projects": {"verification": {"type": "library", "root": "libs/verification", "entryFile": "index", "sourceRoot": "libs/verification/src", "compilerOptions": {"tsConfigPath": "libs/verification/tsconfig.lib.json"}}, "paystack": {"type": "library", "root": "libs/paystack", "entryFile": "index", "sourceRoot": "libs/paystack/src", "compilerOptions": {"tsConfigPath": "libs/paystack/tsconfig.lib.json"}}, "mbs": {"type": "library", "root": "libs/mbs", "entryFile": "index", "sourceRoot": "libs/mbs/src", "compilerOptions": {"tsConfigPath": "libs/mbs/tsconfig.lib.json"}}}}